import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [loginResult, setLoginResult] = useState('');
  const webViewRef = useRef(null);

  const handleSubmit = () => {
    if (!username || !password) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    console.log('Attempting login with:');
    console.log('Username:', username);
    console.log('Password:', password);

    setLoading(true);
    setLoginResult('Attempting to login to GUC website...');

    // Load the GUC login page in hidden WebView
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  // JavaScript code to inject into the WebView for browser auth dialog
  const loginScript = `
    (function() {
      console.log('Script injected - detecting browser authentication dialog...');

      // Send page info immediately
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'pageInfo',
        url: window.location.href,
        title: document.title,
        authDialogDetected: true,
        message: 'Browser authentication dialog detected. Credentials will be handled by WebView props.'
      }));

      // Monitor for any page changes after auth
      let previousUrl = window.location.href;
      setInterval(function() {
        if (window.location.href !== previousUrl) {
          previousUrl = window.location.href;
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'navigation',
            url: window.location.href,
            title: document.title
          }));
        }
      }, 1000);
    })();
  `;

  // Handle messages from WebView
  const onWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('WebView message:', data);

      if (data.type === 'pageInfo') {
        const result = `
Page: ${data.title}
URL: ${data.url}
Username field: ${data.hasUsernameField ? 'Found' : 'Not found'}
Password field: ${data.hasPasswordField ? 'Found' : 'Not found'}
Modal/Popup: ${data.hasModal ? 'Found' : 'Not found'}
Login button: ${data.hasLoginButton ? 'Found' : 'Not found'}

Page preview:
${data.pageHTML || 'No HTML preview available'}
        `;
        setLoginResult(result);
        console.log('Login attempt result:', result);
      } else if (data.type === 'alert') {
        const alertResult = `
Alert detected: ${data.message}
This might be the login interface!
        `;
        setLoginResult(alertResult);
        console.log('Alert detected:', data.message);
      } else if (data.type === 'prompt') {
        const promptResult = `
Prompt detected: ${data.message}
Responded with: ${data.response}
        `;
        setLoginResult(promptResult);
        console.log('Prompt detected:', data.message, 'Response:', data.response);
      }
    } catch (error) {
      console.log('Error parsing WebView message:', error);
      setLoginResult('Error processing login attempt: ' + error.message);
    }
    setLoading(false);
  };

  // Handle WebView navigation changes
  const onNavigationStateChange = (navState) => {
    console.log('Navigation changed:', navState.url);

    // Check if we've been redirected after login
    if (navState.url.includes('student_ext') && !navState.url.includes('login')) {
      setLoginResult('Login appears successful! Redirected to: ' + navState.url);
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* myGUC Title */}
          <Text style={styles.title}>myGUC</Text>

          {/* Username Section */}
          <Text style={styles.label}>Username</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            placeholderTextColor="#9CA3AF"
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Password Section */}
          <Text style={styles.label}>Password</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            placeholderTextColor="#9CA3AF"
            secureTextEntry
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Logging in...' : 'Submit'}
            </Text>
          </TouchableOpacity>

          {/* Login Result Display */}
          {loginResult ? (
            <View style={styles.resultContainer}>
              <Text style={styles.resultTitle}>Login Attempt Result:</Text>
              <Text style={styles.resultText}>{loginResult}</Text>
            </View>
          ) : null}
        </View>

        {/* Hidden WebView for GUC login */}
        <WebView
          ref={webViewRef}
          source={{
            uri: 'https://apps.guc.edu.eg/student_ext/',
            headers: {
              'Authorization': `Basic ${btoa(username + ':' + password)}`
            }
          }}
          style={styles.hiddenWebView}
          onMessage={onWebViewMessage}
          onNavigationStateChange={onNavigationStateChange}
          onHttpError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.log('HTTP Error:', nativeEvent);
            setLoginResult(`HTTP Error: ${nativeEvent.statusCode} - ${nativeEvent.description}`);
            setLoading(false);
          }}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            console.log('WebView Error:', nativeEvent);
            setLoginResult(`WebView Error: ${nativeEvent.description}`);
            setLoading(false);
          }}
          injectedJavaScript={loginScript}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          scalesPageToFit={true}
          mixedContentMode="compatibility"
          allowsInlineMediaPlayback={true}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937', // Dark grey background
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#DC2626', // Red color from GUC logo
    textAlign: 'center',
    marginBottom: 60,
  },
  label: {
    fontSize: 16,
    color: '#F3F4F6', // Light grey text
    marginBottom: 8,
    marginTop: 20,
  },
  input: {
    backgroundColor: '#374151', // Darker grey for input background
    borderWidth: 2,
    borderColor: '#4B5563', // Medium grey border
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#F3F4F6', // Light text color
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#EAB308', // Yellow color from GUC logo
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF', // Grey when disabled
  },
  submitButtonText: {
    color: '#1F2937', // Dark text on yellow button
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultContainer: {
    backgroundColor: '#374151',
    borderRadius: 8,
    padding: 16,
    marginTop: 24,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  resultTitle: {
    color: '#EAB308',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultText: {
    color: '#F3F4F6',
    fontSize: 14,
    lineHeight: 20,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default LoginScreen;
