import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [loginResult, setLoginResult] = useState('');
  const [shouldLoadWebView, setShouldLoadWebView] = useState(false);
  const [submittedCredentials, setSubmittedCredentials] = useState({ username: '', password: '' });
  const [loginAttemptId, setLoginAttemptId] = useState(0); // Unique ID for each login attempt
  const webViewRef = useRef(null);

  const handleSubmit = () => {
    if (!username || !password) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    // Prevent multiple simultaneous login attempts
    if (loading) {
      console.log('Login already in progress, ignoring submit');
      return;
    }

    const newAttemptId = loginAttemptId + 1;
    console.log(`=== LOGIN ATTEMPT #${newAttemptId} ===`);
    console.log('Username:', username);
    console.log('Username length:', username.length);
    console.log('Password:', password);
    console.log('Password length:', password.length);
    console.log('Username has spaces:', username.includes(' '));
    console.log('Password has spaces:', password.includes(' '));

    // Create the Authorization header with current credentials
    const credentialString = username + ':' + password;
    const authHeader = 'Basic ' + btoa(credentialString);
    console.log('Credential string:', credentialString);
    console.log('Authorization header:', authHeader);
    console.log('Decoded credentials:', atob(authHeader.replace('Basic ', '')));

    // Test if btoa/atob is working correctly
    const testDecode = atob(btoa(credentialString));
    console.log('Encode/decode test:', testDecode === credentialString ? 'PASS' : 'FAIL');

    setLoading(true);
    setLoginResult(`
🔐 Login Attempt #${newAttemptId}
Username: ${username}
Authorization: ${authHeader}
Status: Single attempt - Loading WebView...
    `);

    // Store the submitted credentials and trigger WebView loading
    setSubmittedCredentials({ username, password });
    setLoginAttemptId(newAttemptId);
    setShouldLoadWebView(true);
  };

  // JavaScript code to inject into the WebView for browser auth dialog
  const loginScript = `
    (function() {
      console.log('Script injected - detecting browser authentication dialog...');

      // Send page info only once
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'pageInfo',
        url: window.location.href,
        title: document.title,
        authDialogDetected: true,
        message: 'Browser authentication dialog detected. Single login attempt initiated.'
      }));

      // Monitor for page changes after auth (only check a few times, not continuously)
      let previousUrl = window.location.href;
      let checkCount = 0;
      const maxChecks = 10; // Only check 10 times (10 seconds)

      const checkInterval = setInterval(function() {
        checkCount++;
        if (window.location.href !== previousUrl) {
          previousUrl = window.location.href;
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'navigation',
            url: window.location.href,
            title: document.title
          }));
          clearInterval(checkInterval); // Stop checking after successful navigation
        }

        if (checkCount >= maxChecks) {
          clearInterval(checkInterval); // Stop checking after max attempts
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'timeout',
            message: 'Login attempt timed out after 10 seconds'
          }));
        }
      }, 1000);
    })();
  `;

  // Handle messages from WebView
  const onWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('WebView message:', data);

      if (data.type === 'pageInfo') {
        const result = `
🔐 Authentication Status: ${data.authDialogDetected ? 'Browser Auth Dialog Detected' : 'Regular Page'}
📄 Page: ${data.title}
🌐 URL: ${data.url}
💬 Message: ${data.message}
        `;
        setLoginResult(result);
        console.log('Login attempt result:', result);
      } else if (data.type === 'navigation') {
        const navResult = `
🎯 Navigation detected!
🌐 New URL: ${data.url}
📄 Page Title: ${data.title}
✅ Status: ${data.url.includes('Console.aspx') ? 'LOGIN SUCCESSFUL!' : 'Page changed'}
        `;
        setLoginResult(navResult);
        console.log('Navigation:', data.url);

        // If we reached the console page, login was successful
        if (data.url.includes('Console.aspx')) {
          setLoading(false);
        }
      } else if (data.type === 'timeout') {
        const timeoutResult = `
⏰ Login attempt timed out
💬 Message: ${data.message}
🔄 Try again if needed
        `;
        setLoginResult(timeoutResult);
        console.log('Login timeout:', data.message);
        setLoading(false);
      }
    } catch (error) {
      console.log('Error parsing WebView message:', error);
      setLoginResult('❌ Error processing login attempt: ' + error.message);
      setLoading(false);
    }
  };

  // Handle WebView navigation changes
  const onNavigationStateChange = (navState) => {
    console.log('=== NAVIGATION CHANGE ===');
    console.log('URL:', navState.url);
    console.log('Title:', navState.title);
    console.log('Loading:', navState.loading);
    console.log('Can go back:', navState.canGoBack);
    console.log('Can go forward:', navState.canGoForward);

    // Check for specific URL patterns that indicate success or failure
    const isLoginPage = navState.url.includes('login') || navState.url.includes('Login');
    const isConsolePage = navState.url.includes('Console.aspx') || navState.url.includes('console');
    const isErrorPage = navState.url.includes('error') || navState.url.includes('Error');
    const isMainStudentPage = navState.url.includes('student_ext') && !isLoginPage;

    // Update result with detailed navigation info
    setLoginResult(`
🌐 Navigation Update:
URL: ${navState.url}
Title: ${navState.title}
Loading: ${navState.loading ? 'Yes' : 'No'}
Can go back: ${navState.canGoBack ? 'Yes' : 'No'}

🔍 Page Analysis:
Login page: ${isLoginPage ? 'Yes' : 'No'}
Console page: ${isConsolePage ? 'Yes' : 'No'}
Error page: ${isErrorPage ? 'Yes' : 'No'}
Student portal: ${isMainStudentPage ? 'Yes' : 'No'}

Status: ${isConsolePage ? '✅ LOGIN SUCCESS!' :
          isMainStudentPage ? '✅ LOGGED IN!' :
          isLoginPage ? '🔄 Still on login page' :
          isErrorPage ? '❌ Error page detected' :
          '📄 Page loaded'}
    `);

    // Check if we've been redirected after login
    if (isConsolePage || isMainStudentPage) {
      console.log('� LOGIN SUCCESSFUL! Reached:', navState.url);
      setLoading(false);
    } else if ((isLoginPage || isErrorPage) && !navState.loading) {
      console.log('❌ Login failed - on login/error page:', navState.url);
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* myGUC Title */}
          <Text style={styles.title}>myGUC</Text>

          {/* Username Section */}
          <Text style={styles.label}>Username</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            placeholderTextColor="#9CA3AF"
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Password Section */}
          <Text style={styles.label}>Password</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            placeholderTextColor="#9CA3AF"
            secureTextEntry
            autoCapitalize="none"
            autoCorrect={false}
            onSubmitEditing={handleSubmit}
            returnKeyType="go"
          />

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Logging in...' : 'Submit'}
            </Text>
          </TouchableOpacity>

          {/* Login Result Display */}
          {loginResult ? (
            <View style={styles.resultContainer}>
              <Text style={styles.resultTitle}>Login Attempt Result:</Text>
              <Text style={styles.resultText}>{loginResult}</Text>
            </View>
          ) : null}
        </View>

        {/* Hidden WebView for GUC login - Only loads on submit */}
        {shouldLoadWebView && submittedCredentials.username && submittedCredentials.password && loginAttemptId > 0 ? (
          <WebView
            ref={webViewRef}
            source={{
              uri: 'https://apps.guc.edu.eg/student_ext/',
              headers: {
                'Authorization': `Basic ${btoa(submittedCredentials.username + ':' + submittedCredentials.password)}`
              }
            }}
            style={styles.hiddenWebView}
            onMessage={onWebViewMessage}
            onNavigationStateChange={onNavigationStateChange}
            onHttpError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('=== HTTP ERROR DETAILS ===');
              console.log('Status Code:', nativeEvent.statusCode);
              console.log('Description:', nativeEvent.description);
              console.log('URL:', nativeEvent.url);
              console.log('Full nativeEvent:', nativeEvent);

              const errorResult = `
❌ HTTP Error Details:
Status Code: ${nativeEvent.statusCode}
Description: ${nativeEvent.description}
URL: ${nativeEvent.url}
Credentials Used: ${submittedCredentials.username}:${submittedCredentials.password}
Auth Header: Basic ${btoa(submittedCredentials.username + ':' + submittedCredentials.password)}

${nativeEvent.statusCode === 401 ? '🔐 This means the credentials were rejected by the server' : ''}
${nativeEvent.statusCode === 403 ? '🚫 This means access is forbidden' : ''}
              `;
              setLoginResult(errorResult);
              setLoading(false);
            }}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('WebView Error:', nativeEvent);
              setLoginResult(`❌ WebView Error: ${nativeEvent.description}`);
              setLoading(false);
            }}
            injectedJavaScript={loginScript}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={true}
            scalesPageToFit={true}
            mixedContentMode="compatibility"
            allowsInlineMediaPlayback={true}
            key={`login-attempt-${loginAttemptId}`} // Only changes on actual submit, not on typing
          />
        ) : null}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937', // Dark grey background
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#DC2626', // Red color from GUC logo
    textAlign: 'center',
    marginBottom: 60,
  },
  label: {
    fontSize: 16,
    color: '#F3F4F6', // Light grey text
    marginBottom: 8,
    marginTop: 20,
  },
  input: {
    backgroundColor: '#374151', // Darker grey for input background
    borderWidth: 2,
    borderColor: '#4B5563', // Medium grey border
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#F3F4F6', // Light text color
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#EAB308', // Yellow color from GUC logo
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF', // Grey when disabled
  },
  submitButtonText: {
    color: '#1F2937', // Dark text on yellow button
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultContainer: {
    backgroundColor: '#374151',
    borderRadius: 8,
    padding: 16,
    marginTop: 24,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  resultTitle: {
    color: '#EAB308',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultText: {
    color: '#F3F4F6',
    fontSize: 14,
    lineHeight: 20,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default LoginScreen;
