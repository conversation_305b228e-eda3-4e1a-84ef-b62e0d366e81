import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [loginResult, setLoginResult] = useState('');
  const [shouldLoadWebView, setShouldLoadWebView] = useState(false);
  const [submittedCredentials, setSubmittedCredentials] = useState({ username: '', password: '' });
  const [loginAttemptId, setLoginAttemptId] = useState(0); // Unique ID for each login attempt
  const webViewRef = useRef(null);

  const handleSubmit = () => {
    if (!username || !password) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    if (loading) {
      return; // Prevent multiple simultaneous attempts
    }

    const newAttemptId = loginAttemptId + 1;
    console.log(`Login attempt #${newAttemptId} - Username: ${username}`);

    setLoading(true);
    setLoginResult('🔐 Attempting login to GUC website...');

    // Store credentials and trigger WebView
    setSubmittedCredentials({ username, password });
    setLoginAttemptId(newAttemptId);
    setShouldLoadWebView(true);
  };





  const onNavigationStateChange = (navState) => {
    console.log('Navigation:', navState.url);

    const isConsolePage = navState.url.includes('Console.aspx');
    const isLoginPage = navState.url.includes('login');
    const isStudentPage = navState.url.includes('student_ext') && !isLoginPage;

    // Update result
    if (isConsolePage || isStudentPage) {
      setLoginResult('✅ Login successful! Redirected to student portal.');
      setLoading(false);
    } else if (isLoginPage && !navState.loading) {
      setLoginResult('❌ Login failed. Still on login page.');
      setLoading(false);
    } else {
      setLoginResult(`🌐 Loading: ${navState.url}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* myGUC Title */}
          <Text style={styles.title}>myGUC</Text>

          {/* Username Section */}
          <Text style={styles.label}>Username</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            placeholderTextColor="#9CA3AF"
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Password Section */}
          <Text style={styles.label}>Password</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            placeholderTextColor="#9CA3AF"
            secureTextEntry
            autoCapitalize="none"
            autoCorrect={false}
            onSubmitEditing={handleSubmit}
            returnKeyType="go"
          />

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Logging in...' : 'Submit'}
            </Text>
          </TouchableOpacity>

          {/* Login Result Display */}
          {loginResult ? (
            <View style={styles.resultContainer}>
              <Text style={styles.resultTitle}>Login Attempt Result:</Text>
              <Text style={styles.resultText}>{loginResult}</Text>
            </View>
          ) : null}
        </View>

        {/* Hidden WebView for GUC login */}
        {shouldLoadWebView && submittedCredentials.username && submittedCredentials.password ? (
          <WebView
            ref={webViewRef}
            source={{
              uri: 'https://apps.guc.edu.eg/student_ext/',
              headers: {
                'Authorization': `Basic ${btoa(submittedCredentials.username + ':' + submittedCredentials.password)}`
              }
            }}
            style={styles.hiddenWebView}
            onNavigationStateChange={onNavigationStateChange}
            onHttpError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('HTTP Error:', nativeEvent.statusCode, nativeEvent.description);
              setLoginResult(`❌ HTTP Error ${nativeEvent.statusCode}: ${nativeEvent.description}`);
              setLoading(false);
            }}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('WebView Error:', nativeEvent.description);
              setLoginResult(`❌ WebView Error: ${nativeEvent.description}`);
              setLoading(false);
            }}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            key={`login-attempt-${loginAttemptId}`}
          />
        ) : null}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937', // Dark grey background
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#DC2626', // Red color from GUC logo
    textAlign: 'center',
    marginBottom: 60,
  },
  label: {
    fontSize: 16,
    color: '#F3F4F6', // Light grey text
    marginBottom: 8,
    marginTop: 20,
  },
  input: {
    backgroundColor: '#374151', // Darker grey for input background
    borderWidth: 2,
    borderColor: '#4B5563', // Medium grey border
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#F3F4F6', // Light text color
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#EAB308', // Yellow color from GUC logo
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF', // Grey when disabled
  },
  submitButtonText: {
    color: '#1F2937', // Dark text on yellow button
    fontSize: 18,
    fontWeight: 'bold',
  },
  resultContainer: {
    backgroundColor: '#374151',
    borderRadius: 8,
    padding: 16,
    marginTop: 24,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  resultTitle: {
    color: '#EAB308',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  resultText: {
    color: '#F3F4F6',
    fontSize: 14,
    lineHeight: 20,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default LoginScreen;
