import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [loginResult, setLoginResult] = useState('');
  const webViewRef = useRef(null);

  const handleSubmit = () => {
    if (!username || !password) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    console.log('Attempting login with:');
    console.log('Username:', username);
    console.log('Password:', password);

    setLoading(true);
    setLoginResult('Attempting to login to GUC website...');

    // Load the GUC login page in hidden WebView
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  // JavaScript code to inject into the WebView for automatic login
  const loginScript = `
    (function() {
      console.log('Script injected - looking for login form...');

      // Wait for page to load
      setTimeout(function() {
        // Look for username field (common IDs/names for ASP.NET)
        const usernameField = document.getElementById('txtUserName') ||
                             document.getElementById('txtUsername') ||
                             document.querySelector('input[name*="username"]') ||
                             document.querySelector('input[name*="UserName"]') ||
                             document.querySelector('input[type="text"]');

        // Look for password field
        const passwordField = document.getElementById('txtPassword') ||
                             document.querySelector('input[name*="password"]') ||
                             document.querySelector('input[name*="Password"]') ||
                             document.querySelector('input[type="password"]');

        // Look for submit button
        const submitButton = document.getElementById('btnLogin') ||
                            document.querySelector('input[type="submit"]') ||
                            document.querySelector('button[type="submit"]') ||
                            document.querySelector('input[value*="Login"]') ||
                            document.querySelector('input[value*="Sign"]');

        if (usernameField && passwordField) {
          console.log('Found login fields!');
          usernameField.value = '${username}';
          passwordField.value = '${password}';

          // Trigger change events (important for ASP.NET)
          usernameField.dispatchEvent(new Event('change', { bubbles: true }));
          passwordField.dispatchEvent(new Event('change', { bubbles: true }));

          if (submitButton) {
            console.log('Found submit button, clicking...');
            submitButton.click();
          } else {
            console.log('No submit button found');
            // Try to submit the form
            const form = usernameField.closest('form');
            if (form) {
              form.submit();
            }
          }
        } else {
          console.log('Login fields not found');
          console.log('Username field:', usernameField);
          console.log('Password field:', passwordField);
        }

        // Send page info back to React Native
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'pageInfo',
          url: window.location.href,
          title: document.title,
          hasUsernameField: !!usernameField,
          hasPasswordField: !!passwordField,
          hasSubmitButton: !!submitButton
        }));
      }, 2000);
    })();
  `;

  // Handle messages from WebView
  const onWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('WebView message:', data);

      if (data.type === 'pageInfo') {
        const result = `
Page: ${data.title}
URL: ${data.url}
Username field found: ${data.hasUsernameField ? 'Yes' : 'No'}
Password field found: ${data.hasPasswordField ? 'Yes' : 'No'}
Submit button found: ${data.hasSubmitButton ? 'Yes' : 'No'}
        `;
        setLoginResult(result);
        console.log('Login attempt result:', result);
      }
    } catch (error) {
      console.log('Error parsing WebView message:', error);
      setLoginResult('Error processing login attempt');
    }
    setLoading(false);
  };

  // Handle WebView navigation changes
  const onNavigationStateChange = (navState) => {
    console.log('Navigation changed:', navState.url);

    // Check if we've been redirected after login
    if (navState.url.includes('student_ext') && !navState.url.includes('login')) {
      setLoginResult('Login appears successful! Redirected to: ' + navState.url);
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* myGUC Title */}
          <Text style={styles.title}>myGUC</Text>

          {/* Username Section */}
          <Text style={styles.label}>Username</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            placeholderTextColor="#9CA3AF"
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Password Section */}
          <Text style={styles.label}>Password</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            placeholderTextColor="#9CA3AF"
            secureTextEntry
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Logging in...' : 'Submit'}
            </Text>
          </TouchableOpacity>

          {/* Login Result Display */}
          {loginResult ? (
            <View style={styles.resultContainer}>
              <Text style={styles.resultTitle}>Login Attempt Result:</Text>
              <Text style={styles.resultText}>{loginResult}</Text>
            </View>
          ) : null}
        </View>

        {/* Hidden WebView for GUC login */}
        <WebView
          ref={webViewRef}
          source={{ uri: 'https://apps.guc.edu.eg/student_ext/' }}
          style={styles.hiddenWebView}
          onMessage={onWebViewMessage}
          onNavigationStateChange={onNavigationStateChange}
          injectedJavaScript={loginScript}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          scalesPageToFit={true}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937', // Dark grey background
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#DC2626', // Red color from GUC logo
    textAlign: 'center',
    marginBottom: 60,
  },
  label: {
    fontSize: 16,
    color: '#F3F4F6', // Light grey text
    marginBottom: 8,
    marginTop: 20,
  },
  input: {
    backgroundColor: '#374151', // Darker grey for input background
    borderWidth: 2,
    borderColor: '#4B5563', // Medium grey border
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#F3F4F6', // Light text color
    marginBottom: 16,
  },
  submitButton: {
    backgroundColor: '#EAB308', // Yellow color from GUC logo
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  submitButtonText: {
    color: '#1F2937', // Dark text on yellow button
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default LoginScreen;
