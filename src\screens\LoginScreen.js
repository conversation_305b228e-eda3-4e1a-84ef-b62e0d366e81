import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [shouldLoadWebView, setShouldLoadWebView] = useState(false);
  const [submittedCredentials, setSubmittedCredentials] = useState({ username: '', password: '' });
  const [loginAttemptId, setLoginAttemptId] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const webViewRef = useRef(null);

  const handleSubmit = () => {
    if (!username || !password) {
      Alert.alert('Error', 'Please enter both username and password');
      return;
    }

    if (loading) {
      return;
    }

    const newAttemptId = loginAttemptId + 1;
    console.log('Attempting login to GUC website...');

    setLoading(true);

    setSubmittedCredentials({ username, password });
    setLoginAttemptId(newAttemptId);
    setShouldLoadWebView(true);
  };



  const onWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      if (data.type === 'success_html') {
        console.log('=== SUCCESSFUL LOGIN PAGE HTML ===');
        console.log('URL:', data.url);
        console.log('Title:', data.title);
        console.log('HTML Content:');
        console.log(data.html);
        console.log('=== END HTML ===');
      }
    } catch (error) {
      console.log('Error parsing WebView message:', error);
    }
  };

  const onNavigationStateChange = (navState) => {
    // Console log current page after every navigation
    console.log('=== CURRENT PAGE ===');
    console.log('URL:', navState.url);
    console.log('Title:', navState.title);
    console.log('Loading:', navState.loading);
    console.log('==================');

    // Check for successful login - redirect to index.aspx
    if (navState.url.includes('index.aspx') && !navState.loading) {
      console.log('✅ LOGIN SUCCESSFUL! Redirected to index.aspx');
      setLoading(false);

      // Inject JavaScript to get the page HTML
      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(`
          console.log('=== SUCCESSFUL LOGIN PAGE HTML ===');
          console.log(document.documentElement.outerHTML);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'success_html',
            html: document.documentElement.outerHTML,
            url: window.location.href,
            title: document.title
          }));
        `);
      }
    }
    // Check if still on login page (failed login)
    else if ((navState.url.includes('login') || navState.url.includes('Login')) && !navState.loading) {
      console.log('❌ Login failed - still on login page');
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* myGUC Title */}
          <Text style={styles.title}>myGUC</Text>

          {/* Username Section */}
          <Text style={styles.label}>Username</Text>
          <TextInput
            style={styles.input}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter your username"
            placeholderTextColor="#9CA3AF"
            autoCapitalize="none"
            autoCorrect={false}
          />

          {/* Password Section */}
          <Text style={styles.label}>Password</Text>
          <View style={styles.passwordContainer}>
            <TextInput
              style={styles.passwordInput}
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              placeholderTextColor="#9CA3AF"
              secureTextEntry={!showPassword}
              autoCapitalize="none"
              autoCorrect={false}
              onSubmitEditing={handleSubmit}
              returnKeyType="go"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Text style={styles.eyeIcon}>
                {showPassword ? '�' : '👁'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Logging in...' : 'Submit'}
            </Text>
          </TouchableOpacity>


        </View>

        {/* Hidden WebView for GUC login - Only loads on submit */}
        {shouldLoadWebView && submittedCredentials.username && submittedCredentials.password && loginAttemptId > 0 ? (
          <WebView
            ref={webViewRef}
            source={{
              uri: 'https://apps.guc.edu.eg/student_ext/',
              headers: {
                'Authorization': `Basic ${btoa(submittedCredentials.username + ':' + submittedCredentials.password)}`
              }
            }}
            style={styles.hiddenWebView}
            onMessage={onWebViewMessage}
            onNavigationStateChange={onNavigationStateChange}
            onHttpError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('❌ HTTP Error:', nativeEvent.statusCode, nativeEvent.description);
              setLoading(false);
            }}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('❌ WebView Error:', nativeEvent.description);
              setLoading(false);
            }}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={true}
            scalesPageToFit={true}
            mixedContentMode="compatibility"
            allowsInlineMediaPlayback={true}
            key={`login-attempt-${loginAttemptId}`} // Only changes on actual submit, not on typing
          />
        ) : null}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1F2937', // Dark grey background
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 32,
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#DC2626', // Red color from GUC logo
    textAlign: 'center',
    marginBottom: 60,
  },
  label: {
    fontSize: 16,
    color: '#F3F4F6', // Light grey text
    marginBottom: 8,
    marginTop: 20,
  },
  input: {
    backgroundColor: '#374151', // Darker grey for input background
    borderWidth: 2,
    borderColor: '#4B5563', // Medium grey border
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#F3F4F6', // Light text color
    marginBottom: 16,
  },
  passwordContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  passwordInput: {
    backgroundColor: '#374151',
    borderWidth: 2,
    borderColor: '#4B5563',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingRight: 50, // Make room for eye icon
    fontSize: 16,
    color: '#F3F4F6',
  },
  eyeButton: {
    position: 'absolute',
    right: 12,
    top: 12,
    padding: 4,
  },
  eyeIcon: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '600',
  },
  submitButton: {
    backgroundColor: '#EAB308', // Yellow color from GUC logo
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  submitButtonDisabled: {
    backgroundColor: '#9CA3AF', // Grey when disabled
  },
  submitButtonText: {
    color: '#1F2937', // Dark text on yellow button
    fontSize: 18,
    fontWeight: 'bold',
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default LoginScreen;
