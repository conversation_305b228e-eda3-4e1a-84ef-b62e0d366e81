{"name": "myguc", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.15", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "react-native-screens": "~4.11.1", "react-native-safe-area-context": "5.4.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}