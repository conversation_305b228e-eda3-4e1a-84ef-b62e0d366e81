const express = require('express');
const auth = require('basic-auth');

const app = express();
const PORT = 3000;

app.use((req, res, next) => {
  const credentials = auth(req);

  if (!credentials) {
    console.log(`Login attempt failed: No credentials`);
    res.setHeader('WWW-Authenticate', 'Basic realm="Secure Area"');
    res.statusCode = 401;
    res.end('No credentials sent');
    return;
  }

  // Log every attempt with timestamp
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] Login attempt -> Username: "${credentials.name}", Password: "${credentials.pass}"`);

  // For testing: Accept specific credentials, reject others
  if (credentials.name === 'test' && credentials.pass === 'test123') {
    console.log('✅ LOGIN SUCCESS - Credentials accepted!');
    res.statusCode = 200;
    res.setHeader('Content-Type', 'text/html');
    res.end(`
      <html>
        <body>
          <h1>✅ Login Successful!</h1>
          <p>Welcome, ${credentials.name}!</p>
          <p>Your credentials were accepted by the test server.</p>
        </body>
      </html>
    `);
  } else {
    console.log('❌ LOGIN FAILED - Wrong credentials');
    res.setHeader('WWW-Authenticate', 'Basic realm="Test Login Area"');
    res.statusCode = 401;
    res.end('Wrong credentials - try test/test123');
  }
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running at http://localhost:${PORT}`);
  console.log(`Also accessible at http://[YOUR_IP]:${PORT}`);
  console.log('Replace [YOUR_IP] with your actual IP address for mobile testing');
});
