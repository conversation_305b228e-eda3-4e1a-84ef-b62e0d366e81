const express = require('express');
const auth = require('basic-auth');

const app = express();
const PORT = 3000;

app.use((req, res, next) => {
  const credentials = auth(req);

  if (!credentials) {
    console.log(`Login attempt failed: No credentials`);
    res.setHeader('WWW-Authenticate', 'Basic realm="Secure Area"');
    res.statusCode = 401;
    res.end('No credentials sent');
    return;
  }

  // Log every attempt
  console.log(`Login attempt -> Username: ${credentials.name}, Password: ${credentials.pass}`);

  // Reject all attempts (you can customize this)
  res.setHeader('WWW-Authenticate', 'Basic realm="Secure Area"');
  res.statusCode = 401;
  res.end('Wrong credentials');
});

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
